#!/usr/bin/env python3
"""
Test Reddit credentials and provide setup guidance.
"""

import praw
import logging
from prawcore.exceptions import Forbidden, NotFound, TooManyRequests, ServerError

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

def test_credentials():
    """Test Reddit credentials and provide guidance."""
    
    print("=" * 60)
    print("REDDIT CREDENTIALS TEST")
    print("=" * 60)
    
    # Try to import credentials
    try:
        from reddit_credentials import REDDIT_CLIENT_ID, REDDIT_CLIENT_SECRET, REDDIT_USERNAME, REDDIT_PASSWORD
        print("✓ Credentials file found")
        
        # Check if credentials are filled in
        if REDDIT_USERNAME == 'YOUR_REDDIT_USERNAME' or REDDIT_PASSWORD == 'YOUR_REDDIT_PASSWORD':
            print("✗ Credentials not filled in properly")
            print("Please edit reddit_credentials.py and add your actual Reddit username and password")
            return False
            
        print(f"✓ Username: {REDDIT_USERNAME}")
        print(f"✓ Client ID: {REDDIT_CLIENT_ID[:10]}...")
        
    except ImportError:
        print("✗ reddit_credentials.py not found")
        return False
    
    print("\nTesting Reddit connection...")
    
    # Test 1: Try with current credentials
    try:
        reddit = praw.Reddit(
            client_id=REDDIT_CLIENT_ID,
            client_secret=REDDIT_CLIENT_SECRET,
            username=REDDIT_USERNAME,
            password=REDDIT_PASSWORD,
            user_agent='credential_test_v1'
        )
        
        user = reddit.user.me()
        print(f"✓ Successfully authenticated as: {user}")
        
        # Test accessing a subreddit
        subreddit = reddit.subreddit('ExtremelyHairyWomen')
        posts = list(subreddit.hot(limit=1))
        print(f"✓ Successfully accessed ExtremelyHairyWomen: {len(posts)} posts")
        
        return True
        
    except Exception as e:
        print(f"✗ Authentication failed: {e}")
        
        if "unauthorized_client" in str(e):
            print("\n" + "="*60)
            print("REDDIT APP SETUP REQUIRED")
            print("="*60)
            print("The error indicates your Reddit app is not set up correctly.")
            print("You need to create a 'script' type app on Reddit.")
            print()
            print("STEPS TO FIX:")
            print("1. Go to: https://www.reddit.com/prefs/apps")
            print("2. Click 'Create App' or 'Create Another App'")
            print("3. Fill in the form:")
            print("   - Name: Reddit Scraper (or any name)")
            print("   - App type: Select 'script'")
            print("   - Description: (optional)")
            print("   - About URL: (leave blank)")
            print("   - Redirect URI: http://localhost:8080")
            print("4. Click 'Create app'")
            print("5. Note the client ID (appears under the app name)")
            print("6. Note the client secret")
            print("7. Update reddit_credentials.py with the new client ID and secret")
            print()
            print("Alternative: Try using read-only access without authentication")
            
        elif "invalid_grant" in str(e):
            print("\n" + "="*60)
            print("INVALID CREDENTIALS")
            print("="*60)
            print("Your username or password is incorrect.")
            print("Please check:")
            print("1. Reddit username is correct (case sensitive)")
            print("2. Reddit password is correct")
            print("3. Two-factor authentication is disabled (not supported)")
            
        return False

def test_readonly_access():
    """Test if read-only access works without authentication."""
    print("\n" + "="*60)
    print("TESTING READ-ONLY ACCESS")
    print("="*60)
    
    try:
        from reddit_credentials import REDDIT_CLIENT_ID, REDDIT_CLIENT_SECRET
        
        reddit = praw.Reddit(
            client_id=REDDIT_CLIENT_ID,
            client_secret=REDDIT_CLIENT_SECRET,
            user_agent='readonly_test_v1'
        )
        
        # Test accessing a safe subreddit
        subreddit = reddit.subreddit('python')
        posts = list(subreddit.hot(limit=1))
        print(f"✓ Read-only access works: {len(posts)} posts from r/python")
        
        # Test accessing the problematic subreddit
        subreddit = reddit.subreddit('ExtremelyHairyWomen')
        posts = list(subreddit.hot(limit=1))
        print(f"✓ Read-only access to NSFW subreddit works: {len(posts)} posts")
        
        return True
        
    except Exception as e:
        print(f"✗ Read-only access failed: {e}")
        return False

def main():
    success = test_credentials()
    
    if not success:
        print("\nTrying read-only access as fallback...")
        readonly_success = test_readonly_access()
        
        if readonly_success:
            print("\n" + "="*60)
            print("RECOMMENDATION")
            print("="*60)
            print("Read-only access works! You can use the script without authentication.")
            print("Update your script to remove username/password authentication.")
        else:
            print("\n" + "="*60)
            print("NEXT STEPS")
            print("="*60)
            print("1. Set up a proper Reddit 'script' app (see instructions above)")
            print("2. Or try using a different Reddit account")
            print("3. Or check Reddit's API status")

if __name__ == '__main__':
    main()
