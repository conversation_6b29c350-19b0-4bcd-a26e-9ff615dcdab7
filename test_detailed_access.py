#!/usr/bin/env python3
"""
Test what specific Reddit operations are being blocked.
"""

import praw
import time
import logging

# Import credentials
try:
    from reddit_credentials import REDDIT_CLIENT_ID, REDDIT_CLIENT_SECRET
except ImportError:
    print("Error: reddit_credentials.py not found!")
    exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

def test_operations(reddit, subreddit_name):
    """Test different Reddit operations to see what's blocked."""
    
    print(f"\n{'='*60}")
    print(f"TESTING OPERATIONS ON: r/{subreddit_name}")
    print(f"{'='*60}")
    
    try:
        subreddit = reddit.subreddit(subreddit_name)
        
        # Test 1: Basic subreddit info
        print("1. Testing basic subreddit access...")
        try:
            display_name = subreddit.display_name
            print(f"   ✓ Display name: {display_name}")
        except Exception as e:
            print(f"   ✗ Display name failed: {e}")
            return False
            
        # Test 2: Subscriber count
        print("2. Testing subscriber count...")
        try:
            subscribers = subreddit.subscribers
            print(f"   ✓ Subscribers: {subscribers:,}")
        except Exception as e:
            print(f"   ✗ Subscribers failed: {e}")
            
        # Test 3: Hot posts (what worked before)
        print("3. Testing hot posts (limit 5)...")
        try:
            hot_posts = list(subreddit.hot(limit=5))
            print(f"   ✓ Hot posts: {len(hot_posts)} retrieved")
        except Exception as e:
            print(f"   ✗ Hot posts failed: {e}")
            
        # Test 4: New posts (what we want)
        print("4. Testing new posts (limit 5)...")
        try:
            new_posts = list(subreddit.new(limit=5))
            print(f"   ✓ New posts: {len(new_posts)} retrieved")
        except Exception as e:
            print(f"   ✗ New posts failed: {e}")
            return False
            
        # Test 5: New posts (limit 100)
        print("5. Testing new posts (limit 100)...")
        try:
            new_posts_100 = list(subreddit.new(limit=100))
            print(f"   ✓ New posts (100): {len(new_posts_100)} retrieved")
        except Exception as e:
            print(f"   ✗ New posts (100) failed: {e}")
            return False
            
        # Test 6: Author information
        print("6. Testing author information...")
        if new_posts:
            try:
                first_post = new_posts[0]
                if first_post.author:
                    author = first_post.author
                    print(f"   ✓ Author: {author.name}")
                    
                    # Test author details
                    try:
                        karma = author.link_karma
                        print(f"   ✓ Author link karma: {karma}")
                    except Exception as e:
                        print(f"   ✗ Author karma failed: {e}")
                        return False
                        
                    try:
                        created = author.created_utc
                        age_days = (time.time() - created) / (24 * 3600)
                        print(f"   ✓ Author age: {age_days:.1f} days")
                    except Exception as e:
                        print(f"   ✗ Author age failed: {e}")
                        return False
                        
                else:
                    print("   ⚠ First post has no author (deleted)")
            except Exception as e:
                print(f"   ✗ Author access failed: {e}")
                return False
        
        # Test 7: Subreddit rules
        print("7. Testing subreddit rules...")
        try:
            rules = list(subreddit.rules)
            print(f"   ✓ Rules: {len(rules)} retrieved")
        except Exception as e:
            print(f"   ✗ Rules failed: {e}")
            
        print(f"\n✅ ALL TESTS PASSED for r/{subreddit_name}")
        return True
        
    except Exception as e:
        print(f"\n❌ FAILED to access r/{subreddit_name}: {e}")
        return False

def main():
    # Initialize Reddit
    reddit = praw.Reddit(
        client_id=REDDIT_CLIENT_ID,
        client_secret=REDDIT_CLIENT_SECRET,
        user_agent='detailed_access_test_v1'
    )
    
    print("🔍 TESTING DETAILED REDDIT ACCESS")
    print("This will help identify what operations are being blocked.")
    
    # Test different types of subreddits
    test_subreddits = [
        'python',              # Safe, popular programming subreddit
        'leagueoflegends',     # Gaming subreddit (was working before)
        'programming',         # Another safe tech subreddit
        'ExtremelyHairyWomen', # NSFW subreddit (original problem)
    ]
    
    results = {}
    
    for subreddit_name in test_subreddits:
        success = test_operations(reddit, subreddit_name)
        results[subreddit_name] = success
        
        print("\nPress Enter to continue to next subreddit...")
        input()
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    
    for subreddit, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"r/{subreddit}: {status}")
    
    successful = sum(results.values())
    total = len(results)
    
    if successful == 0:
        print(f"\n❌ ALL TESTS FAILED ({successful}/{total})")
        print("This suggests Reddit API access is completely blocked.")
        print("Possible causes:")
        print("1. Reddit API policy changes")
        print("2. Rate limiting")
        print("3. Need for OAuth authentication")
        print("4. IP-based restrictions")
    elif successful < total:
        print(f"\n⚠ PARTIAL SUCCESS ({successful}/{total})")
        print("Some subreddits are accessible, others are not.")
        print("This suggests content-based restrictions (NSFW, private, etc.)")
    else:
        print(f"\n✅ ALL TESTS PASSED ({successful}/{total})")
        print("Reddit API access is working correctly!")

if __name__ == '__main__':
    main()
