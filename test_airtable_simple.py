#!/usr/bin/env python3
"""
Simple test to debug Airtable field mapping.
"""

import requests
import json
from datetime import datetime

# Airtable configuration
AIRTABLE_ACCESS_TOKEN = "**********************************************************************************"
AIRTABLE_BASE_ID = "appN5XyvYsL2ZnwyE"
AIRTABLE_TABLE_ID = "tbl6Ig7z99bs8MTcJ"

def test_simple_upload():
    """Test uploading a simple record to debug field issues."""
    
    url = f"https://api.airtable.com/v0/{AIRTABLE_BASE_ID}/{AIRTABLE_TABLE_ID}"
    headers = {
        "Authorization": f"Bearer {AIRTABLE_ACCESS_TOKEN}",
        "Content-Type": "application/json"
    }
    
    # Test with minimal data first
    test_record = {
        "fields": {
            "Subreddit": "test_subreddit",
            "Last Check Date": datetime.now().isoformat()
        }
    }
    
    print("🧪 Testing minimal record upload...")
    print(f"Data: {json.dumps(test_record, indent=2)}")
    
    response = requests.post(url, headers=headers, json=test_record)
    
    if response.status_code == 200:
        print("✅ Minimal record uploaded successfully!")
        record_id = response.json()['id']
        
        # Now test with Members field
        test_record_2 = {
            "fields": {
                "Subreddit": "test_with_members",
                "Members": 1000000,
                "Last Check Date": datetime.now().isoformat()
            }
        }
        
        print("\n🧪 Testing with Members field...")
        print(f"Data: {json.dumps(test_record_2, indent=2)}")
        
        response2 = requests.post(url, headers=headers, json=test_record_2)
        
        if response2.status_code == 200:
            print("✅ Members field works with integer!")
        else:
            print(f"❌ Members field failed: {response2.status_code} - {response2.text}")
            
            # Try with string
            test_record_3 = {
                "fields": {
                    "Subreddit": "test_with_members_string",
                    "Members": "1000000",
                    "Last Check Date": datetime.now().isoformat()
                }
            }
            
            print("\n🧪 Testing Members as string...")
            response3 = requests.post(url, headers=headers, json=test_record_3)
            
            if response3.status_code == 200:
                print("✅ Members field works with string!")
            else:
                print(f"❌ Members field failed as string too: {response3.status_code} - {response3.text}")
        
    else:
        print(f"❌ Minimal record failed: {response.status_code} - {response.text}")

def get_table_schema():
    """Get the table schema to see field types."""
    
    # First, let's see what fields exist by getting existing records
    url = f"https://api.airtable.com/v0/{AIRTABLE_BASE_ID}/{AIRTABLE_TABLE_ID}?maxRecords=1"
    headers = {
        "Authorization": f"Bearer {AIRTABLE_ACCESS_TOKEN}",
    }
    
    print("🔍 Getting table schema...")
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('records'):
            fields = data['records'][0]['fields']
            print("📋 Existing fields in table:")
            for field_name, field_value in fields.items():
                print(f"  {field_name}: {field_value} ({type(field_value).__name__})")
        else:
            print("📋 No existing records to analyze")
    else:
        print(f"❌ Failed to get schema: {response.status_code} - {response.text}")

def main():
    print("🔧 AIRTABLE FIELD DEBUGGING")
    print("=" * 40)
    
    get_table_schema()
    print()
    test_simple_upload()

if __name__ == '__main__':
    main()
