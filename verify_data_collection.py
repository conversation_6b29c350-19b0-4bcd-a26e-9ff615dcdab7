#!/usr/bin/env python3
"""
Verification script to check what data is being collected from subreddits.
This helps ensure the main script is working correctly.
"""

import praw
import time
import logging
from prawcore.exceptions import Forbidden, NotFound

# Import credentials
try:
    from reddit_credentials import REDDIT_CLIENT_ID, REDDIT_CLIENT_SECRET
except ImportError:
    print("Error: reddit_credentials.py not found!")
    exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

def detailed_subreddit_analysis(reddit, subreddit_name):
    """Perform detailed analysis of a subreddit to verify data collection."""
    
    print("=" * 80)
    print(f"DETAILED ANALYSIS: r/{subreddit_name}")
    print("=" * 80)
    
    try:
        subreddit = reddit.subreddit(subreddit_name)
        
        # Basic subreddit info
        print(f"Display Name: {subreddit.display_name}")
        try:
            print(f"Subscribers: {subreddit.subscribers:,}")
        except:
            print("Subscribers: Unable to fetch")
            
        try:
            print(f"NSFW: {subreddit.over18}")
        except:
            print("NSFW: Unable to fetch")
            
        print(f"Description: {subreddit.public_description[:100]}..." if subreddit.public_description else "No description")
        
        print("\n" + "-" * 40)
        print("HOT POSTS ANALYSIS")
        print("-" * 40)
        
        # Get hot posts
        posts = list(subreddit.hot(limit=10))
        print(f"Total posts retrieved: {len(posts)}")
        
        if not posts:
            print("❌ No posts found!")
            return None
            
        # Analyze posts
        total_score = 0
        post_details = []
        
        for i, post in enumerate(posts[:5]):  # Show first 5 posts
            score = post.score
            age_hours = (time.time() - post.created_utc) / 3600
            total_score += score
            
            post_details.append({
                'title': post.title[:50] + "..." if len(post.title) > 50 else post.title,
                'score': score,
                'age_hours': age_hours,
                'comments': post.num_comments,
                'author': str(post.author) if post.author else "[deleted]"
            })
            
            print(f"Post {i+1}:")
            print(f"  Title: {post_details[i]['title']}")
            print(f"  Score: {score}")
            print(f"  Age: {age_hours:.1f} hours")
            print(f"  Comments: {post.num_comments}")
            print(f"  Author: {post_details[i]['author']}")
            print()
        
        # Calculate engagement metrics
        avg_upvotes = total_score / len(posts)
        max_age = max((time.time() - post.created_utc for post in posts), default=1)
        engagement_score = avg_upvotes / max_age
        
        print("-" * 40)
        print("ENGAGEMENT METRICS")
        print("-" * 40)
        print(f"Average upvotes: {avg_upvotes:.2f}")
        print(f"Max post age: {max_age/3600:.1f} hours")
        print(f"Engagement score: {engagement_score:.8f}")
        
        # Try to get rules
        print("\n" + "-" * 40)
        print("SUBREDDIT RULES")
        print("-" * 40)
        
        try:
            rules = list(subreddit.rules)  # Use iterator instead of calling
            print(f"Total rules: {len(rules)}")
            
            min_link_karma = 0
            min_comment_karma = 0
            min_account_age = 0
            
            for i, rule in enumerate(rules[:3]):  # Show first 3 rules
                print(f"Rule {i+1}: {rule.short_name}")
                print(f"  Description: {rule.description[:100]}...")
                
                # Check for karma/age requirements
                rule_text = f"{rule.short_name} {rule.description}".lower()
                if 'karma' in rule_text:
                    if 'link' in rule_text:
                        min_link_karma = 1
                    if 'comment' in rule_text:
                        min_comment_karma = 1
                if 'account age' in rule_text or 'account must be' in rule_text:
                    min_account_age = 1
                print()
            
            print(f"Detected requirements:")
            print(f"  Min link karma required: {min_link_karma}")
            print(f"  Min comment karma required: {min_comment_karma}")
            print(f"  Min account age required: {min_account_age}")
            
        except Exception as e:
            print(f"Could not fetch rules: {e}")
            min_link_karma = min_comment_karma = min_account_age = 0
        
        # Final score calculation
        barrier_score = min_link_karma + min_comment_karma + min_account_age
        final_score = engagement_score / (barrier_score + 1)
        
        print("\n" + "-" * 40)
        print("FINAL METRICS")
        print("-" * 40)
        print(f"Barrier score: {barrier_score}")
        print(f"Final engagement score: {final_score:.8f}")
        
        return {
            'subreddit': subreddit.display_name,
            'posts_found': len(posts),
            'avg_upvotes': avg_upvotes,
            'engagement_score': engagement_score,
            'final_score': final_score,
            'min_link_karma': min_link_karma,
            'min_comment_karma': min_comment_karma,
            'min_account_age': min_account_age
        }
        
    except Exception as e:
        print(f"❌ Error analyzing {subreddit_name}: {e}")
        return None

def main():
    # Initialize Reddit
    reddit = praw.Reddit(
        client_id=REDDIT_CLIENT_ID,
        client_secret=REDDIT_CLIENT_SECRET,
        user_agent='data_verification_script_v1'
    )
    
    # Test subreddits - mix of different types
    test_subreddits = [
        'squirting',           # The one you mentioned
        'ExtremelyHairyWomen', # The original problematic one
        'leagueoflegends',     # Popular gaming subreddit
        'programming'          # Safe tech subreddit
    ]
    
    print("🔍 REDDIT DATA COLLECTION VERIFICATION")
    print("This script shows exactly what data is being collected from each subreddit.")
    print()
    
    results = []
    
    for subreddit_name in test_subreddits:
        result = detailed_subreddit_analysis(reddit, subreddit_name)
        if result:
            results.append(result)
        
        print("\n" + "="*80)
        print("Press Enter to continue to next subreddit...")
        input()
    
    # Summary
    print("\n" + "="*80)
    print("SUMMARY OF ALL ANALYZED SUBREDDITS")
    print("="*80)
    
    for result in results:
        print(f"r/{result['subreddit']}:")
        print(f"  Posts found: {result['posts_found']}")
        print(f"  Avg upvotes: {result['avg_upvotes']:.2f}")
        print(f"  Final score: {result['final_score']:.8f}")
        print(f"  Requirements: Link({result['min_link_karma']}) Comment({result['min_comment_karma']}) Age({result['min_account_age']})")
        print()
    
    print("✅ Data collection verification complete!")
    print("If you see reasonable numbers above, your main script is working correctly.")

if __name__ == '__main__':
    main()
