#!/usr/bin/env python3
"""
Test script to check Reddit API access for specific subreddits.
This helps diagnose 403 errors and access issues.
"""

import praw
import logging
from prawcore.exceptions import Forbidden, NotFound, TooManyRequests, ServerError

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

def test_subreddit_access(reddit, subreddit_name):
    """Test if a subreddit is accessible and what data we can get."""
    try:
        logging.info(f"Testing access to: {subreddit_name}")
        subreddit = reddit.subreddit(subreddit_name)
        
        # Test basic properties
        try:
            display_name = subreddit.display_name
            logging.info(f"  ✓ Display name: {display_name}")
        except Exception as e:
            logging.error(f"  ✗ Cannot get display name: {e}")
            return False
            
        try:
            subscribers = subreddit.subscribers
            logging.info(f"  ✓ Subscribers: {subscribers:,}")
        except Exception as e:
            logging.warning(f"  ⚠ Cannot get subscribers: {e}")
            
        try:
            over18 = subreddit.over18
            logging.info(f"  ✓ NSFW: {over18}")
        except Exception as e:
            logging.warning(f"  ⚠ Cannot get NSFW status: {e}")
            
        # Test getting posts
        try:
            posts = list(subreddit.hot(limit=5))
            logging.info(f"  ✓ Retrieved {len(posts)} hot posts")
            if posts:
                logging.info(f"    Sample post: '{posts[0].title[:50]}...'")
        except Exception as e:
            logging.error(f"  ✗ Cannot get posts: {e}")
            return False
            
        # Test getting rules
        try:
            rules = list(subreddit.rules())
            logging.info(f"  ✓ Retrieved {len(rules)} rules")
        except Exception as e:
            logging.warning(f"  ⚠ Cannot get rules: {e}")
            
        return True
        
    except Forbidden as e:
        logging.error(f"  ✗ FORBIDDEN (403): {e}")
        return False
    except NotFound as e:
        logging.error(f"  ✗ NOT FOUND (404): {e}")
        return False
    except Exception as e:
        logging.error(f"  ✗ UNEXPECTED ERROR: {e}")
        return False

def main():
    # Initialize Reddit instance
    reddit = praw.Reddit(
        client_id='5By3t8CS5mX38dsL9oT49g',
        client_secret='e7nfwjDAKCDmIkLarUJfDZcZpJpRVg',
        user_agent='reddit_access_tester_v1'
    )
    
    # Test Reddit connection
    try:
        user = reddit.user.me()
        if user:
            logging.info(f"Connected as user: {user}")
        else:
            logging.info("Connected with read-only access (no user authentication)")
    except Exception as e:
        logging.warning(f"Cannot get user info: {e}")
    
    # Test subreddits - mix of safe and NSFW
    test_subreddits = [
        'ExtremelyHairyWomen',  # The problematic one
        'leagueoflegends',      # Safe gaming subreddit
        'StarWars',             # Safe entertainment subreddit
        'gonewild',             # Popular NSFW subreddit
        'hentai',               # NSFW anime subreddit
        'programming',          # Safe tech subreddit
    ]
    
    logging.info("=" * 60)
    logging.info("REDDIT API ACCESS TEST")
    logging.info("=" * 60)
    
    accessible = 0
    total = len(test_subreddits)
    
    for subreddit_name in test_subreddits:
        if test_subreddit_access(reddit, subreddit_name):
            accessible += 1
        logging.info("-" * 40)
    
    logging.info("=" * 60)
    logging.info(f"SUMMARY: {accessible}/{total} subreddits accessible")
    
    if accessible < total:
        logging.warning("Some subreddits are not accessible. This could be due to:")
        logging.warning("1. Reddit API restrictions on NSFW content")
        logging.warning("2. Subreddits being private, quarantined, or banned")
        logging.warning("3. Rate limiting or temporary server issues")
        logging.warning("4. Need for OAuth authentication for NSFW content")

if __name__ == '__main__':
    main()
