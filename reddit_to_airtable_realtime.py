#!/usr/bin/env python3
"""
Reddit Subreddit Data Collector with Real-time Airtable Upload
Processes subreddits and uploads each one to Airtable immediately.
"""

import argparse
import csv
import time
import logging
import praw
import random
import requests
from datetime import datetime
from prawcore.exceptions import Forbidden, NotFound, TooManyRequests, ServerError

# Import credentials
try:
    from reddit_credentials import REDDIT_CLIENT_ID, REDDIT_CLIENT_SECRET, REDDIT_USERNAME, REDDIT_PASSWORD
    print("✓ Reddit credentials loaded successfully")
except ImportError:
    print("Error: reddit_credentials.py not found!")
    print("You need: REDDIT_CLIENT_ID, REDDIT_CLIENT_SECRET, REDDIT_USERNAME, REDDIT_PASSWORD")
    exit(1)

# Airtable configuration
AIRTABLE_ACCESS_TOKEN = "**********************************************************************************"
AIRTABLE_BASE_ID = "appN5XyvYsL2ZnwyE"
AIRTABLE_TABLE_ID = "tbl6Ig7z99bs8MTcJ"

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

def upload_to_airtable(subreddit_data):
    """Upload a single subreddit record to Airtable immediately."""
    
    url = f"https://api.airtable.com/v0/{AIRTABLE_BASE_ID}/{AIRTABLE_TABLE_ID}"
    headers = {
        "Authorization": f"Bearer {AIRTABLE_ACCESS_TOKEN}",
        "Content-Type": "application/json"
    }
    
    # Get current timestamp
    current_time = datetime.now().isoformat()
    
    # Map data to Airtable fields (all as strings)
    airtable_record = {
        "fields": {
            "Subreddit": str(subreddit_data['subreddit_name']),
            "Members": str(subreddit_data['subscribers']),
            "Last Check Date": current_time,
            "Engagement Score": str(round(float(subreddit_data['engagement_score']), 4)),
            "Min Post Karma": str(subreddit_data['min_post_karma']),
            "Min Comment Karma": str(subreddit_data['min_comment_karma']),
            "Min Account Age": str(round(float(subreddit_data['min_account_age_days']), 2)),
            "Subreddit Rules": str(subreddit_data['subreddit_rules'])
        }
    }
    
    try:
        response = requests.post(url, headers=headers, json=airtable_record)
        
        if response.status_code == 200:
            logging.info(f"📤 ✅ Uploaded to Airtable: {subreddit_data['subreddit_name']}")
            return True
        else:
            logging.error(f"📤 ❌ Airtable upload failed for {subreddit_data['subreddit_name']}: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logging.error(f"📤 ❌ Airtable upload error for {subreddit_data['subreddit_name']}: {e}")
        return False

def fetch_subreddit_data(reddit, subreddit_name, max_retries=3):
    """Fetch subreddit data - same as before but streamlined."""
    for attempt in range(max_retries):
        try:
            logging.info(f"Processing subreddit: {subreddit_name} (attempt {attempt + 1}/{max_retries})")
            
            if attempt > 0:
                delay = random.uniform(2, 5) * attempt
                logging.info(f"Waiting {delay:.1f} seconds before retry...")
                time.sleep(delay)
            
            subreddit = reddit.subreddit(subreddit_name)
            
            # Get basic info
            try:
                display_name = subreddit.display_name
                subscribers = subreddit.subscribers
                logging.info(f"  Subreddit: {display_name}, Subscribers: {subscribers:,}")
            except Forbidden:
                logging.error(f"Access forbidden to subreddit {subreddit_name}")
                return None
            except Exception as e:
                logging.error(f"Cannot access basic subreddit info for {subreddit_name}: {e}")
                return None

            # Get 100 newest posts
            logging.info(f"  Fetching 100 newest posts...")
            try:
                posts = list(subreddit.new(limit=100))
            except Exception as e:
                logging.error(f"Cannot fetch newest posts for {subreddit_name}: {e}")
                return None
                
            if len(posts) < 50:
                logging.warning(f"Insufficient posts in {subreddit_name}: only {len(posts)} posts found")
                return None

            # Calculate metrics
            total_upvotes = sum(post.score for post in posts)
            avg_upvotes = total_upvotes / len(posts)
            
            newest_post_time = max(post.created_utc for post in posts)
            oldest_post_time = min(post.created_utc for post in posts)
            time_span_hours = (newest_post_time - oldest_post_time) / 3600
            
            # Get poster metrics
            logging.info(f"  Analyzing poster metrics...")
            poster_account_ages = []
            poster_link_karma = []
            poster_comment_karma = []
            failed_poster_lookups = 0
            
            for post in posts:
                try:
                    if post.author is None:
                        failed_poster_lookups += 1
                        continue
                        
                    author = post.author
                    account_age_days = (time.time() - author.created_utc) / (24 * 3600)
                    poster_account_ages.append(account_age_days)
                    poster_link_karma.append(author.link_karma)
                    poster_comment_karma.append(author.comment_karma)
                    
                except Exception as e:
                    failed_poster_lookups += 1
                    continue
            
            # Check data quality
            successful_lookups = len(poster_account_ages)
            if successful_lookups < len(posts) * 0.7:
                logging.error(f"Insufficient poster data for {subreddit_name}: only {successful_lookups}/{len(posts)} successful lookups")
                return None
            
            if not poster_account_ages:
                logging.error(f"No valid poster data found for {subreddit_name}")
                return None
            
            # Calculate minimums
            min_account_age_days = min(poster_account_ages)
            min_link_karma = min(poster_link_karma)
            min_comment_karma = min(poster_comment_karma)
            
            # Get rules
            rules_text = ""
            try:
                rules = list(subreddit.rules)
                rules_text = " | ".join([f"{rule.short_name}: {rule.description}" for rule in rules])
                logging.info(f"  Retrieved {len(rules)} subreddit rules")
            except Exception as e:
                logging.warning(f"Could not fetch rules for {subreddit_name}: {e}")
                rules_text = "Rules unavailable"

            logging.info(f"✔ Successfully processed {subreddit_name}")
            logging.info(f"  Posts: {len(posts)}, Avg upvotes: {avg_upvotes:.1f}, Min age: {min_account_age_days:.1f} days")
            
            return {
                'subreddit_name': display_name,
                'subscribers': subscribers,
                'engagement_score': avg_upvotes / max(time_span_hours, 1),
                'min_account_age_days': min_account_age_days,
                'min_post_karma': min_link_karma,
                'min_comment_karma': min_comment_karma,
                'avg_upvotes_per_post': avg_upvotes,
                'time_span_hours': time_span_hours,
                'subreddit_rules': rules_text
            }

        except TooManyRequests as e:
            wait_time = 60 * (attempt + 1)
            logging.warning(f"Rate limited for {subreddit_name}. Waiting {wait_time} seconds...")
            time.sleep(wait_time)
            continue
            
        except Exception as e:
            logging.error(f"Unexpected error with subreddit {subreddit_name}: {e}")
            if attempt == max_retries - 1:
                return None
            time.sleep(2 * (attempt + 1))
            continue
    
    logging.error(f"Failed to fetch data for {subreddit_name} after {max_retries} attempts")
    return None

def main():
    parser = argparse.ArgumentParser(description='Reddit to Airtable Real-time Uploader')
    parser.add_argument('--delay', type=float, default=2.0, help='Delay between requests in seconds')
    parser.add_argument('--start_from', type=int, default=0, help='Start processing from this line number (0-based)')
    parser.add_argument('--max_subreddits', type=int, default=None, help='Maximum number of subreddits to process')
    args = parser.parse_args()

    # Reddit API setup
    reddit = praw.Reddit(
        client_id=REDDIT_CLIENT_ID,
        client_secret=REDDIT_CLIENT_SECRET,
        username=REDDIT_USERNAME,
        password=REDDIT_PASSWORD,
        user_agent='reddit_to_airtable_realtime_v1'
    )

    # Test connections
    try:
        user = reddit.user.me()
        logging.info(f"✓ Reddit authentication successful: {user}")
        
        test_subreddit = reddit.subreddit('python')
        test_posts = list(test_subreddit.new(limit=1))
        logging.info(f"✓ Reddit access successful: {len(test_posts)} posts retrieved")
    except Exception as e:
        logging.error(f"Failed to connect to Reddit: {e}")
        return

    # Test Airtable
    try:
        url = f"https://api.airtable.com/v0/{AIRTABLE_BASE_ID}/{AIRTABLE_TABLE_ID}?maxRecords=1"
        headers = {"Authorization": f"Bearer {AIRTABLE_ACCESS_TOKEN}"}
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            logging.info("✓ Airtable connection successful")
        else:
            logging.error(f"Airtable connection failed: {response.status_code}")
            return
    except Exception as e:
        logging.error(f"Failed to connect to Airtable: {e}")
        return

    # Load subreddits
    with open("cleaned.txt", "r") as f:
        all_subreddits = [line.strip() for line in f.readlines() if line.strip()]
    
    subreddits = all_subreddits[args.start_from:]
    if args.max_subreddits:
        subreddits = subreddits[:args.max_subreddits]
    
    logging.info(f"🚀 Starting real-time processing of {len(subreddits)} subreddits")
    logging.info(f"📊 Results will appear in Airtable immediately after each subreddit is processed")
    
    successful = 0
    failed = 0
    airtable_uploads = 0
    
    for i, name in enumerate(subreddits):
        clean_name = name.lstrip('/r/')
        
        logging.info(f"Progress: {i+1}/{len(subreddits)} ({((i+1)/len(subreddits)*100):.1f}%)")
        
        # Process subreddit
        data = fetch_subreddit_data(reddit, clean_name)
        if data:
            successful += 1
            
            # Upload to Airtable immediately
            if upload_to_airtable(data):
                airtable_uploads += 1
            
            logging.info(f"✅ Success: {successful}, Failed: {failed}, Uploaded: {airtable_uploads}")
        else:
            failed += 1
            logging.warning(f"❌ Skipped: {clean_name} (Success: {successful}, Failed: {failed})")
        
        # Delay between requests
        if i < len(subreddits) - 1:
            time.sleep(args.delay)

    logging.info(f"🏁 Processing complete!")
    logging.info(f"📊 Final stats: {successful} successful, {failed} failed, {airtable_uploads} uploaded to Airtable")
    logging.info(f"🔗 Check your Airtable: https://airtable.com/appN5XyvYsL2ZnwyE/tbl6Ig7z99bs8MTcJ/viwTCE7gOgi219UjR")

if __name__ == '__main__':
    main()
