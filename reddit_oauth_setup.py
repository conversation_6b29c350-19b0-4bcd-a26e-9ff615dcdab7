#!/usr/bin/env python3
"""
Reddit OAuth Setup Script
This script helps you set up proper OAuth authentication for Reddit API access.
"""

import praw
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

def setup_reddit_oauth():
    """Set up Reddit with OAuth authentication."""
    
    print("=" * 60)
    print("REDDIT OAUTH SETUP")
    print("=" * 60)
    print()
    
    print("To fix the 403 errors, you need to set up OAuth authentication.")
    print("Here are your options:")
    print()
    
    print("OPTION 1: Script Application (Recommended for your use case)")
    print("- Go to: https://www.reddit.com/prefs/apps")
    print("- Click 'Create App' or 'Create Another App'")
    print("- Choose 'script' as the app type")
    print("- Set redirect URI to: http://localhost:8080")
    print("- Note down the client ID (under the app name)")
    print("- Note down the client secret")
    print()
    
    print("OPTION 2: Use existing credentials with username/password")
    print("- You can use your current client_id and client_secret")
    print("- But you need to add your Reddit username and password")
    print()
    
    # Test current setup
    print("Testing current setup...")
    try:
        reddit = praw.Reddit(
            client_id='5By3t8CS5mX38dsL9oT49g',
            client_secret='e7nfwjDAKCDmIkLarUJfDZcZpJpRVg',
            user_agent='reddit_oauth_test_v1'
        )
        
        # Try to access a simple subreddit
        subreddit = reddit.subreddit('python')
        posts = list(subreddit.hot(limit=1))
        print("✓ Current setup works! No OAuth needed.")
        return True
        
    except Exception as e:
        print(f"✗ Current setup failed: {e}")
        print()
        
    # Prompt for OAuth setup
    print("Would you like to set up OAuth authentication now? (y/n): ", end="")
    choice = input().lower().strip()
    
    if choice == 'y':
        setup_oauth_interactive()
    else:
        print("OAuth setup skipped. You can run this script again later.")
        print()
        print("Alternative: Try using a different Reddit app or check if your")
        print("current app has the right permissions in Reddit's app settings.")
    
    return False

def setup_oauth_interactive():
    """Interactive OAuth setup."""
    print()
    print("INTERACTIVE OAUTH SETUP")
    print("-" * 30)
    
    print("Enter your Reddit username: ", end="")
    username = input().strip()
    
    print("Enter your Reddit password: ", end="")
    password = input().strip()
    
    print()
    print("Testing OAuth authentication...")
    
    try:
        reddit = praw.Reddit(
            client_id='5By3t8CS5mX38dsL9oT49g',
            client_secret='e7nfwjDAKCDmIkLarUJfDZcZpJpRVg',
            username=username,
            password=password,
            user_agent='reddit_oauth_test_authenticated_v1'
        )
        
        # Test authentication
        user = reddit.user.me()
        print(f"✓ Successfully authenticated as: {user}")
        
        # Test subreddit access
        subreddit = reddit.subreddit('ExtremelyHairyWomen')
        posts = list(subreddit.hot(limit=1))
        print(f"✓ Successfully accessed subreddit posts: {len(posts)} posts retrieved")
        
        # Save credentials to a config file
        config_content = f'''# Reddit API Configuration
# Add these to your script:

reddit = praw.Reddit(
    client_id='5By3t8CS5mX38dsL9oT49g',
    client_secret='e7nfwjDAKCDmIkLarUJfDZcZpJpRVg',
    username='{username}',
    password='{password}',
    user_agent='engagement_tracker_script_authenticated'
)
'''
        
        with open('reddit_config.txt', 'w') as f:
            f.write(config_content)
        
        print("✓ Configuration saved to 'reddit_config.txt'")
        print()
        print("You can now use these credentials in your main script!")
        return True
        
    except Exception as e:
        print(f"✗ OAuth authentication failed: {e}")
        print()
        print("Possible issues:")
        print("1. Incorrect username/password")
        print("2. Two-factor authentication enabled (not supported with password auth)")
        print("3. Reddit API restrictions")
        print()
        print("Try creating a new Reddit app at: https://www.reddit.com/prefs/apps")
        return False

def main():
    setup_reddit_oauth()

if __name__ == '__main__':
    main()
