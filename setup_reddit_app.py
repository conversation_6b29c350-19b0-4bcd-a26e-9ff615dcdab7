#!/usr/bin/env python3
"""
Helper script to guide you through setting up a Reddit script app.
"""

import webbrowser
import time

def main():
    print("🔧 REDDIT SCRIPT APP SETUP GUIDE")
    print("=" * 50)
    print()
    print("You're getting 'unauthorized_client' error because your Reddit app")
    print("is not configured as a 'script' type app.")
    print()
    print("STEP 1: Create a Reddit Script App")
    print("-" * 35)
    print("1. I'll open Reddit's app creation page for you")
    print("2. Click 'Create App' or 'Create Another App'")
    print("3. Fill out the form:")
    print("   - Name: Reddit Data Scraper (or any name)")
    print("   - App type: SELECT 'script' ⚠️ (IMPORTANT!)")
    print("   - Description: (optional)")
    print("   - About URL: (leave blank)")
    print("   - Redirect URI: http://localhost:8080")
    print("4. Click 'Create app'")
    print()
    
    input("Press Enter to open Reddit's app creation page...")
    
    try:
        webbrowser.open('https://www.reddit.com/prefs/apps')
        print("✓ Opened Reddit apps page in your browser")
    except:
        print("✗ Could not open browser automatically")
        print("Please manually go to: https://www.reddit.com/prefs/apps")
    
    print()
    print("STEP 2: Get Your New Credentials")
    print("-" * 35)
    print("After creating the app, you'll see:")
    print("- Client ID: A short string under the app name")
    print("- Client Secret: A longer string labeled 'secret'")
    print()
    
    print("Enter your NEW credentials below:")
    print()
    
    client_id = input("Client ID: ").strip()
    client_secret = input("Client Secret: ").strip()
    
    if not client_id or not client_secret:
        print("❌ Error: Both Client ID and Secret are required!")
        return
    
    # Read current credentials
    try:
        with open('reddit_credentials.py', 'r') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ Error: reddit_credentials.py not found!")
        return
    
    # Update credentials
    lines = content.split('\n')
    new_lines = []
    
    for line in lines:
        if line.startswith('REDDIT_CLIENT_ID'):
            new_lines.append(f"REDDIT_CLIENT_ID = '{client_id}'")
        elif line.startswith('REDDIT_CLIENT_SECRET'):
            new_lines.append(f"REDDIT_CLIENT_SECRET = '{client_secret}'")
        else:
            new_lines.append(line)
    
    # Write updated credentials
    with open('reddit_credentials.py', 'w') as f:
        f.write('\n'.join(new_lines))
    
    print()
    print("✅ Updated reddit_credentials.py with new credentials!")
    print()
    print("STEP 3: Test the Setup")
    print("-" * 20)
    print("Now let's test if everything works...")
    
    # Test the new credentials
    try:
        import praw
        
        # Get username and password from file
        exec(open('reddit_credentials.py').read())
        
        reddit = praw.Reddit(
            client_id=locals()['REDDIT_CLIENT_ID'],
            client_secret=locals()['REDDIT_CLIENT_SECRET'],
            username=locals()['REDDIT_USERNAME'],
            password=locals()['REDDIT_PASSWORD'],
            user_agent='reddit_setup_test_v1'
        )
        
        # Test authentication
        user = reddit.user.me()
        print(f"✅ SUCCESS! Authenticated as: {user}")
        
        # Test subreddit access
        subreddit = reddit.subreddit('python')
        posts = list(subreddit.new(limit=1))
        print(f"✅ SUCCESS! Can access subreddit posts: {len(posts)} posts retrieved")
        
        print()
        print("🎉 SETUP COMPLETE!")
        print("Your Reddit script app is working correctly.")
        print("You can now run your main script:")
        print("python3 RedditSubredditChecker.py --max_subreddits 2")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print()
        print("Common issues:")
        print("1. Make sure you selected 'script' as the app type")
        print("2. Check that your username/password are correct")
        print("3. Disable 2-factor authentication if enabled")
        print("4. Wait a few minutes and try again (Reddit API delay)")

if __name__ == '__main__':
    main()
